/**
 * Test setup file for Vitest
 * Configures global mocks and test environment
 */

import { vi } from 'vitest'

// Mock Node.js built-in modules
vi.mock('node:child_process', () => ({
  exec: vi.fn(),
}))

vi.mock('node:fs', () => ({
  promises: {
    readdir: vi.fn(),
    access: vi.fn(),
    unlink: vi.fn(),
    stat: vi.fn(),
  },
}))

vi.mock('node:path', () => ({
  default: {
    join: vi.fn((...args: string[]) => args.join('/')),
    resolve: vi.fn((...args: string[]) => args.join('/')),
    dirname: vi.fn((path: string) => path.split('/').slice(0, -1).join('/')),
    basename: vi.fn((path: string) => path.split('/').pop() || ''),
    extname: vi.fn((path: string) => {
      const parts = path.split('.')
      return parts.length > 1 ? `.${parts.pop()}` : ''
    }),
  },
}))

// Mock external dependencies
vi.mock('@inquirer/checkbox', () => ({
  default: vi.fn(),
}))

vi.mock('@inquirer/password', () => ({
  default: vi.fn(),
}))

vi.mock('commander', () => ({
  Command: vi.fn().mockImplementation(() => ({
    name: vi.fn().mockReturnThis(),
    description: vi.fn().mockReturnThis(),
    version: vi.fn().mockReturnThis(),
    helpOption: vi.fn().mockReturnThis(),
    option: vi.fn().mockReturnThis(),
    addHelpText: vi.fn().mockReturnThis(),
    parse: vi.fn().mockReturnThis(),
    opts: vi.fn().mockReturnValue({}),
  })),
}))

// Mock process methods that might cause issues in tests
const originalExit = process.exit
const originalConsoleLog = console.log
const originalConsoleError = console.error
const originalConsoleWarn = console.warn

beforeEach(() => {
  // Mock process.exit to prevent tests from actually exiting
  process.exit = vi.fn() as never
  
  // Mock console methods to reduce noise in tests
  console.log = vi.fn()
  console.error = vi.fn()
  console.warn = vi.fn()
  
  // Reset all mocks before each test
  vi.clearAllMocks()
})

afterEach(() => {
  // Restore original functions after each test
  process.exit = originalExit
  console.log = originalConsoleLog
  console.error = originalConsoleError
  console.warn = originalConsoleWarn
})

// Global test utilities
global.createMockApp = (overrides: Partial<import('../types.ts').AppInfo> = {}) => ({
  alreadyInstalled: false,
  appPath: '/Applications/TestApp.app',
  brewName: 'test-app',
  brewType: 'cask' as const,
  originalName: 'TestApp',
  status: 'available' as const,
  ...overrides,
})

global.createMockCommandResult = (overrides: Partial<import('../types.ts').BrewCommandResult> = {}) => ({
  exitCode: 0,
  stderr: '',
  stdout: 'success',
  success: true,
  ...overrides,
})

// Declare global types for TypeScript
declare global {
  function createMockApp(overrides?: Partial<import('../types.ts').AppInfo>): import('../types.ts').AppInfo
  function createMockCommandResult(overrides?: Partial<import('../types.ts').BrewCommandResult>): import('../types.ts').BrewCommandResult
}
