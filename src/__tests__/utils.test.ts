/**
 * Tests for utility functions
 */

import { describe, expect, it, vi } from 'vitest'

import {
  capitalize,
  colorize,
  createLogger,
  createProgressBar,
  escapeShellArgument,
  extractAppName,
  formatDuration,
  formatList,
  groupBy,
  isEmpty,
  isValidAppName,
  isValidBrewPackageName,
  normalizeAppName,
  parseCommandOutput,
  pluralize,
  sleep,
  truncate,
  uniqueBy,
} from '../utils.ts'

describe('utils', () => {
  describe('capitalize', () => {
    it('should capitalize the first letter of a string', () => {
      expect(capitalize('hello')).toBe('Hello')
      expect(capitalize('WORLD')).toBe('WORLD')
      expect(capitalize('a')).toBe('A')
      expect(capitalize('')).toBe('')
    })
  })

  describe('colorize', () => {
    it('should add color codes to text', () => {
      const result = colorize('test', 'RED')
      expect(result).toContain('test')
      expect(result).toMatch(/\u001B\[\d+m.*\u001B\[0m/)
    })
  })

  describe('createLogger', () => {
    it('should create a logger with all required methods', () => {
      const logger = createLogger(false)
      expect(logger).toHaveProperty('debug')
      expect(logger).toHaveProperty('error')
      expect(logger).toHaveProperty('info')
      expect(logger).toHaveProperty('verbose')
      expect(logger).toHaveProperty('warn')
    })

    it('should log verbose messages when verbose is true', () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
      const logger = createLogger(true)
      
      logger.verbose('test message')
      expect(consoleSpy).toHaveBeenCalled()
      
      consoleSpy.mockRestore()
    })

    it('should not log verbose messages when verbose is false', () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
      const logger = createLogger(false)
      
      logger.verbose('test message')
      expect(consoleSpy).not.toHaveBeenCalled()
      
      consoleSpy.mockRestore()
    })
  })

  describe('createProgressBar', () => {
    it('should create a progress bar with correct format', () => {
      const bar = createProgressBar(5, 10, 20)
      expect(bar).toMatch(/\[.*\] \d+% \(\d+\/\d+\)/)
      expect(bar).toContain('50%')
      expect(bar).toContain('(5/10)')
    })

    it('should handle edge cases', () => {
      expect(createProgressBar(0, 10)).toContain('0%')
      expect(createProgressBar(10, 10)).toContain('100%')
      expect(createProgressBar(15, 10)).toContain('100%') // Should cap at 100%
    })
  })

  describe('escapeShellArgument', () => {
    it('should escape shell arguments properly', () => {
      expect(escapeShellArgument('simple')).toBe('"simple"')
      expect(escapeShellArgument('with spaces')).toBe('"with spaces"')
      expect(escapeShellArgument('with"quotes')).toBe('"with\\"quotes"')
      expect(escapeShellArgument('')).toBe('""')
    })
  })

  describe('extractAppName', () => {
    it('should extract app name from path', () => {
      expect(extractAppName('/Applications/TestApp.app')).toBe('TestApp')
      expect(extractAppName('/Applications/Google Chrome.app')).toBe('Google Chrome')
      expect(extractAppName('TestApp.app')).toBe('TestApp')
      expect(extractAppName('/invalid/path')).toBe('path')
      expect(extractAppName('')).toBe('')
    })
  })

  describe('formatDuration', () => {
    it('should format durations correctly', () => {
      expect(formatDuration(500)).toBe('500ms')
      expect(formatDuration(1500)).toBe('1s')
      expect(formatDuration(65000)).toBe('1m 5s')
      expect(formatDuration(3661000)).toBe('61m 1s')
    })
  })

  describe('formatList', () => {
    it('should format lists with bullets', () => {
      const items = ['item1', 'item2', 'item3']
      const result = formatList(items)
      expect(result).toContain('• item1')
      expect(result).toContain('• item2')
      expect(result).toContain('• item3')
    })

    it('should handle custom indentation', () => {
      const items = ['item1']
      const result = formatList(items, '    ')
      expect(result).toContain('    • item1')
    })
  })

  describe('groupBy', () => {
    it('should group items by key function', () => {
      const items = [
        { type: 'a', value: 1 },
        { type: 'b', value: 2 },
        { type: 'a', value: 3 },
      ]
      const grouped = groupBy(items, item => item.type)
      
      expect(grouped.a).toHaveLength(2)
      expect(grouped.b).toHaveLength(1)
      expect(grouped.a[0].value).toBe(1)
      expect(grouped.a[1].value).toBe(3)
    })
  })

  describe('isEmpty', () => {
    it('should detect empty strings', () => {
      expect(isEmpty('')).toBe(true)
      expect(isEmpty('   ')).toBe(true)
      expect(isEmpty(null)).toBe(true)
      expect(isEmpty(undefined)).toBe(true)
      expect(isEmpty('hello')).toBe(false)
      expect(isEmpty(' hello ')).toBe(false)
    })
  })

  describe('isValidAppName', () => {
    it('should validate app names', () => {
      expect(isValidAppName('ValidApp')).toBe(true)
      expect(isValidAppName('Valid App')).toBe(true)
      expect(isValidAppName('Valid-App')).toBe(true)
      expect(isValidAppName('')).toBe(false)
      expect(isValidAppName('   ')).toBe(false)
    })
  })

  describe('isValidBrewPackageName', () => {
    it('should validate brew package names', () => {
      expect(isValidBrewPackageName('valid-package')).toBe(true)
      expect(isValidBrewPackageName('valid_package')).toBe(true)
      expect(isValidBrewPackageName('validpackage')).toBe(true)
      expect(isValidBrewPackageName('valid package')).toBe(false)
      expect(isValidBrewPackageName('')).toBe(false)
    })
  })

  describe('normalizeAppName', () => {
    it('should normalize app names for Homebrew', () => {
      expect(normalizeAppName('Google Chrome')).toBe('google-chrome')
      expect(normalizeAppName('Visual Studio Code')).toBe('visual-studio-code')
      expect(normalizeAppName('App-Name (Beta)')).toBe('app-name-beta')
      expect(normalizeAppName('  Multiple   Spaces  ')).toBe('multiple-spaces')
      expect(normalizeAppName('Special!@#$%Characters')).toBe('specialcharacters')
    })
  })

  describe('parseCommandOutput', () => {
    it('should parse command output into lines', () => {
      const output = 'line1\nline2\n\nline3\n'
      const result = parseCommandOutput(output)
      expect(result).toEqual(['line1', 'line2', 'line3'])
    })

    it('should handle empty output', () => {
      expect(parseCommandOutput('')).toEqual([])
      expect(parseCommandOutput('\n\n\n')).toEqual([])
    })
  })

  describe('pluralize', () => {
    it('should pluralize words correctly', () => {
      expect(pluralize('app', 1)).toBe('app')
      expect(pluralize('app', 2)).toBe('apps')
      expect(pluralize('app', 0)).toBe('apps')
      expect(pluralize('child', 2, 'ren')).toBe('children')
    })
  })

  describe('sleep', () => {
    it('should resolve after specified time', async () => {
      const start = Date.now()
      await sleep(10)
      const end = Date.now()
      expect(end - start).toBeGreaterThanOrEqual(9) // Allow for some timing variance
    })
  })

  describe('truncate', () => {
    it('should truncate long strings', () => {
      expect(truncate('hello world', 10)).toBe('hello w...')
      expect(truncate('short', 10)).toBe('short')
      expect(truncate('exactly10c', 10)).toBe('exactly10c')
    })
  })

  describe('uniqueBy', () => {
    it('should remove duplicates by key function', () => {
      const items = [
        { id: 1, name: 'a' },
        { id: 2, name: 'b' },
        { id: 1, name: 'c' },
      ]
      const unique = uniqueBy(items, item => item.id)
      expect(unique).toHaveLength(2)
      expect(unique[0].name).toBe('a')
      expect(unique[1].name).toBe('b')
    })
  })
})
